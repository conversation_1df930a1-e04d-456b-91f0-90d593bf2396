import BigNumber from "bignumber.js";
import Tooltip from "rc-tooltip";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import Link from "next/link";
import {
  BurntIcon,
  CheftHatIcon,
  DexscreenerIcon,
  FlashIcon,
  GroupUser,
  HoldersIcon,
  // MouseIcon,
  SearchIcon,
} from "@/assets/icons";
import {
  AppAvatarToken,
  AppCopy,
  AppLogoNetwork,
  AppNumber,
  AppProcessCircle,
  AppTimeDisplay,
} from "@/components";
import { useOrder } from "@/hooks/useOrder";
import { RootState } from "@/store";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { TPair } from "@/types";
import { DEXS, getDexLogoUrl, getDexName } from "@/utils/dex";
import { formatNumber, formatToPercent } from "@/utils/format";
import { multipliedBN, dividedBN, getTimeFormatBoots } from "@/utils/helper";
import { getCirculatingSupply } from "@/utils/pair";
import rf from "@/services/RequestFactory";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import moment from "moment";
import { AppDispatch } from "@/store";
import Image from "next/image";
import { PairSocials } from "@/components/Pair/Socials";
import { toastSuccess } from "@/libs/toast";
import { getLinkTxExplorer } from "@/utils/helper";
import { useExternalWallet } from "@/hooks";
import { toastError } from "../../libs/toast";
import { useNetwork } from "@/context/network";
import { getSuiBalanceOnchain } from "../../utils/suiClient";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { SUI_DECIMALS } from "../../utils/contants";
import { convertMistToDec } from "../../utils/helper";

const getClassColor = (value: string | number | BigNumber) => {
  if (new BigNumber(value).comparedTo(10_000) < 0) {
    return "text-white-1000";
  }

  if (new BigNumber(value).comparedTo(30_000) < 0) {
    return "text-blue-500";
  }

  if (new BigNumber(value).comparedTo(30_000) > 0) {
    return "text-[#FCD34B]";
  }

  return "text-white-1000";
};

export const MemeItem = ({
  buyAmount,
  meme,
  isGraduated,
  filterParams,
}: {
  buyAmount: string;
  isGraduated?: boolean;
  meme: TPair & {
    funzoneDex?: keyof typeof DEXS;
    funzoneSlug?: string;
    funzonePoolId?: string;
  };
  filterParams?: any;
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { accessToken, network } = useSelector(
    (state: RootState) => state.user
  );
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );

  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const { quickBuy } = useOrder();
  const dispatch = useDispatch<AppDispatch>();
  const { onBuyToken } = useExternalWallet();
  const currentAccount = useCurrentAccount();
  const { currentNetwork } = useNetwork();

  const onBuySuccess = async (digest?: string) => {
    toastSuccess(
      "Success",
      `You bought ${buyAmount} ${meme?.tokenBase?.symbol}`,
      {
        link: getLinkTxExplorer(currentNetwork, digest || ""),
        text: "View Explorer",
      }
    );
    setIsLoading(false);
  };

  const handleQuickBuy = async (e: any) => {
    e.preventDefault();
    if (isLoading) return;

    if (isExternalWallet) {
      const balanceSui = await getSuiBalanceOnchain(
        currentAccount?.address || ""
      );

      if (
        !+balanceSui ||
        new BigNumber(buyAmount).gt(
          new BigNumber(convertMistToDec(balanceSui, SUI_DECIMALS))
        )
      ) {
        return toastError("Error", "Insufficient Balance");
      }

      let pair = meme;
      if (isGraduated && !!meme?.graduatedSlug) {
        pair = await rf
          .getRequest("PairRequest")
          .getPair(network, meme?.graduatedSlug);
      }

      onBuyToken(pair, buyAmount, setIsLoading, onBuySuccess).then();
      return;
    }

    if (!accessToken) return;

    if (!wallets.length) {
      dispatch(setIsShowModalAddWallet({ isShow: true }));
      return;
    }

    let pair = meme;
    if (isGraduated && !!meme?.graduatedSlug) {
      pair = await rf
        .getRequest("PairRequest")
        .getPair(network, meme?.graduatedSlug);
    }
    quickBuy(null, pair, buyAmount, SUI_TOKEN_ADDRESS_FULL, false).then();
  };

  const mCapUsd = multipliedBN(
    meme?.tokenBase?.priceUsd,
    getCirculatingSupply(meme)
  );

  const getPercent = () => {
    if (isGraduated) {
      return "100%";
    }
    return formatToPercent(
      BigNumber.min(
        meme?.bondingCurve || "0",
        // getBondingCurveProcessingByDex(
        //   meme?.dex?.dex,
        //   meme.reserveQuote,
        //   meme.reserveBase,
        // ),
        1
      )
    );
  };
  // if (!!filterParams && !isFilterValidPair(meme, filterParams)) return null;

  const getMemeDexLogo = () => {
    if (isGraduated) {
      return getDexLogoUrl(meme?.funzoneDex as keyof typeof DEXS);
    }
    return getDexLogoUrl(meme?.dex?.dex as keyof typeof DEXS);
  };

  const getMemeDexName = () => {
    if (isGraduated) {
      return getDexName(meme?.funzoneDex as keyof typeof DEXS);
    }
    return getDexName(meme?.dex?.dex as keyof typeof DEXS);
  };

  const getMemeProgress = () => {
    if (isGraduated) {
      return 100;
    }
    return BigNumber.min(meme?.bondingCurve || 0, 1)
      .multipliedBy(100)
      .toFixed(2)
      .toString();
  };

  return (
    <Link href={`/${network}/${meme.slug}`}>
      <div className="border-white-25 hover:bg-white-50 bg-white-25 mb-2 flex items-center gap-2 rounded-[8px] border px-[6px] py-[8px] md:px-[12px] md:py-[12px]">
        <Tooltip overlay={`${getPercent()}`} placement="bottom">
          <div className="tablet:w-[64px] tablet:h-[64px] relative h-[56px] w-[56px] cursor-pointer">
            <AppProcessCircle
              size={isTabletOrMobile ? 56 : 64}
              progress={getMemeProgress()}
            />
            <div className="absolute left-[2px] right-[2px] top-[2px] p-[2px]">
              <AppAvatarToken
                image={
                  meme?.tokenBase?.logoImageUrl || meme?.tokenBase?.iconUrl
                }
                size={56}
                className="tablet:w-[56px] !aspect-square !h-auto w-[48px] object-cover"
              />
            </div>
            <div className="bg-black-800 absolute bottom-[3px] right-[2px] h-[14px] w-[14px] rounded-full p-[2px]">
              <Image
                src={getMemeDexLogo()}
                className="h-[10px] w-[10px] rounded-full"
                alt={getMemeDexName()}
                width={10}
                height={10}
                unoptimized
              />
            </div>
          </div>
        </Tooltip>

        <div className="flex-1">
          <div className="border-white-25 flex justify-between border-b pb-[6px]">
            <div>
              <div className="flex items-center gap-1">
                <div className="text-[12px] font-medium leading-[1.2] md:text-[14px]">
                  {meme?.tokenBase?.symbol}
                </div>
                <div className="text-white-700 max-w-[40px] truncate whitespace-nowrap text-[12px] font-normal leading-[1.2] md:text-[14px] lg:max-w-full">
                  {meme?.tokenBase?.name}
                </div>

                <div onClick={(e) => e.preventDefault()}>
                  <AppCopy
                    message={meme?.tokenBase?.address}
                    className="text-white-700 h-[12px] w-[12px]"
                  />
                </div>

                <Tooltip
                  overlay={<span>Search on Twitter</span>}
                  placement="bottom"
                  overlayClassName="whitespace-nowrap body-xs-regular-8"
                >
                  <span
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      window.open(
                        `https://x.com/search?q=${meme?.tokenBase?.address}`,
                        "_blank",
                        "noopener,noreferrer"
                      );
                    }}
                    className="cursor-pointer"
                  >
                    <SearchIcon className="text-white-500" />
                  </span>
                </Tooltip>

                {!!meme?.tokenBase?.isDexscreenerVerified && (
                  <Tooltip
                    overlay="DexScreener Social Updated"
                    placement="bottom"
                    overlayClassName="whitespace-nowrap"
                  >
                    <DexscreenerIcon className="text-white-500 h-[14px] w-[14px]" />
                  </Tooltip>
                )}

                {meme?.tokenBase?.boostFactor &&
                  moment(
                    meme?.tokenBase?.isBoostedUntil,
                    "YYYY-MM-DD H:mm:ss.S Z"
                  ).valueOf() > moment().valueOf() && (
                    <div className="body-sm-medium-12 flex items-center text-orange-500">
                      <FlashIcon className="h-4 w-4" />
                      {getTimeFormatBoots(meme?.tokenBase?.boostFactor || 0)}
                    </div>
                  )}
              </div>

              <div className="tablet:flex-col desktop:flex-row flex gap-[4px]">
                <div className="desktop:border-r tablet:border-0 border-white-50 body-xs-medium-10 md:body-sm-medium-12 text-white-500 border-r pr-[4px]">
                  <AppTimeDisplay
                    timestamp={+meme.timestamp * 1000}
                    isAgo
                    suffix=""
                  />
                </div>

                <div className="flex gap-[4px]">
                  <Tooltip
                    overlay={
                      meme?.tokenBase?.top10HolderPercent > 15
                        ? `Top 10 holding is more than 15% at (${
                            +meme?.tokenBase?.top10HolderPercent?.toFixed(2) ||
                            "0"
                          }%)`
                        : `Top 10 holding is less than 15% at (${
                            +meme?.tokenBase?.top10HolderPercent?.toFixed(2) ||
                            "0"
                          }%)`
                    }
                    placement="top"
                  >
                    <div
                      className={`body-xs-medium-10 md:body-sm-medium-12 flex cursor-pointer items-center gap-1 ${
                        +meme?.tokenBase?.top10HolderPercent > 16
                          ? "text-red-500"
                          : "text-green-500"
                      }`}
                    >
                      <HoldersIcon />
                      {+meme?.tokenBase?.top10HolderPercent?.toFixed(2) || "0"}%
                    </div>
                  </Tooltip>
                  <Tooltip
                    overlay={
                      meme?.tokenBase?.deployerBalancePercent &&
                      meme?.tokenBase?.deployerBalancePercent > 10
                        ? `Dev holds ${
                            meme?.tokenBase?.deployerBalancePercent?.toFixed(
                              2
                            ) || "0"
                          }%`
                        : `Dev holds ${
                            meme?.tokenBase?.deployerBalancePercent?.toFixed(
                              2
                            ) || "0"
                          }%`
                    }
                    placement="top"
                  >
                    <div
                      className={`
                        body-xs-medium-10 
                        md:body-sm-medium-12 
                        flex 
                        cursor-pointer 
                        items-center 
                        gap-1 
                        ${
                          (meme?.tokenBase?.deployerBalancePercent ?? 0) <= 10
                            ? "text-green-500"
                            : "text-red-500"
                        }
                      `}
                    >
                      <CheftHatIcon />
                      {`${
                        meme?.tokenBase?.deployerBalancePercent?.toFixed(2) ||
                        "0"
                      }%`}
                    </div>
                  </Tooltip>

                  {/* <Tooltip
                    overlay={'Percentage held by insiders'}
                    placement="top"
                  >
                    <div className="body-xs-medium-10 md:body-sm-medium-12 flex gap-1 items-center cursor-pointer">
                      <MouseIcon />
                      --
                    </div>
                  </Tooltip> */}

                  <div className="border-white-100 border-l"></div>

                  <Tooltip
                    overlay={
                      <div>
                        <div className="item-center flex gap-1">
                          <div>Token Burnt</div>
                          {formatNumber(meme?.tokenBase.amountBurned)}
                        </div>
                        <div className="item-center flex gap-1">
                          <div>Burnt rate</div>
                          {formatToPercent(
                            dividedBN(
                              meme?.tokenBase.amountBurned,
                              meme?.tokenBase.totalSupply
                            )
                          )}
                        </div>
                      </div>
                    }
                    placement="top"
                  >
                    <div className="body-xs-medium-10 md:body-sm-medium-12 flex cursor-pointer items-center gap-1">
                      <BurntIcon />
                      {formatToPercent(
                        dividedBN(
                          meme?.tokenBase.amountBurned,
                          meme?.tokenBase.totalSupply
                        )
                      )}
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
            {(accessToken || isExternalWallet) && (
              <div
                onClick={handleQuickBuy}
                className="text-brand-500 hover:bg-brand-900 border-brand-800 desktop:min-w-[80px] flex h-max min-w-[60px] cursor-pointer items-center justify-center gap-1 rounded-[6px] border p-[4px] md:h-full md:p-[8px]"
              >
                <FlashIcon className="h-[12px] w-[12px]" />
                <div className="body-sm-medium-12 flex items-center">
                  {buyAmount}
                </div>

                <AppLogoNetwork
                  network={"sui"}
                  isBase
                  className="h-[12px] w-[12px]"
                />
              </div>
            )}
          </div>

          <div className="mt-[6px] flex flex-col items-start justify-between gap-1 lg:flex-row lg:items-center">
            <div className="flex items-center gap-1">
              <div className="bg-white-50 text-white-500 border-white-50 body-xs-medium-10 flex h-[18px] items-center gap-1 rounded-full border p-[2px]">
                <Image
                  src={getDexLogoUrl(meme?.dex?.dex as keyof typeof DEXS)}
                  className="h-[12px] w-[12px] rounded-full grayscale"
                  alt={getDexName(meme?.dex?.dex as keyof typeof DEXS)}
                  width={12}
                  height={12}
                  unoptimized
                />
                {!isGraduated && <div className="pr-[2px]">{getPercent()}</div>}
              </div>
              <div className="flex items-center gap-1">
                <PairSocials
                  info={meme?.tokenBase?.socials}
                  classNameWrapper={"flex gap-1"}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Tooltip overlay={"Total Holders"} placement="top">
                <div className="flex cursor-pointer items-center gap-1">
                  <GroupUser className="text-white-500" />
                  <div className="body-xs-medium-10 md:body-sm-medium-12">
                    <AppNumber value={meme?.tokenBase?.holdersCount} />
                  </div>
                </div>
              </Tooltip>
              <Tooltip overlay={"Transactions"} placement="top">
                <div className="flex cursor-pointer items-center gap-1">
                  <div className="text-white-500 body-xs-regular-10 md:body-sm-regular-12">
                    Txs
                  </div>
                  <div className="body-xs-medium-10 md:body-sm-medium-12">
                    <AppNumber value={meme.totalTxns} />
                  </div>
                </div>
              </Tooltip>
              <Tooltip overlay={"Volume"} placement="top">
                <div className="flex cursor-pointer items-center gap-1">
                  <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                    Vol
                  </div>
                  <div className="body-xs-medium-10 md:body-sm-medium-12">
                    <AppNumber value={meme?.volumeUsd} isForUSD />
                  </div>
                </div>
              </Tooltip>
              <Tooltip overlay={"Market Cap"} placement="top">
                <div className="flex cursor-pointer items-center gap-1">
                  <div className="body-xs-regular-10 md:body-sm-regular-12 text-white-500">
                    MC
                  </div>
                  <div className="body-xs-medium-10 md:body-sm-medium-12">
                    <AppNumber
                      value={mCapUsd}
                      isForUSD
                      className={getClassColor(mCapUsd || 0)}
                    />
                  </div>
                </div>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};
