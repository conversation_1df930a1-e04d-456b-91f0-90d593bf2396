import { AppDataTableRealtime } from "@/components/AppDataTableRealtime";
import { useCallback, useEffect, useRef, useState } from "react";
// import { FilterByType } from 'src/pages/pool/components/FilterTransaction';
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TOrder, TPair } from "@/types";
import { filterParams } from "@/utils/helper";
import { useSelector } from "react-redux";
// import { ModalConfirm } from 'src/modals';
import { CopyIcon, ExternalLink } from "@/assets/icons";
import {
  AppLogoNetwork,
  AppNumber,
  AppTimeDisplay,
  BaseToken,
} from "@/components";
import { TypeTx } from "@/components/Table/TableMyTrade";
import { OrderStatus } from "@/enums";
import { BREAKPOINT } from "@/enums/responsive.enum";
import { useInitialing } from "@/hooks/useInitialing";
import useWindowSize from "@/hooks/useWindowSize";
import Storage from "@/libs/storage";
import { DATE_TYPE, NETWORKS } from "@/utils/contants";
import { formatShortAddress } from "@/utils/format";
import {
  copyToClipboard,
  getLinkAddressExplorer,
  getSymbolTokenNative,
} from "@/utils/helper";
import moment from "moment";
import { useRouter } from "next/navigation";
import { useMediaQuery } from "react-responsive";
import AppDateTypeSwitch from "../AppDateTypeSwitch";
import BigNumber from "bignumber.js";
import { useNetwork } from "@/context";
import { AppAvatarToken, AppAvatarTokenQuote } from "../AppAvatarToken";

const orderStatusToText = {
  [OrderStatus.SUCCESS]: {
    text: "Success",
    style: "bg-green-800 text-green-500",
  },
  [OrderStatus.PENDING]: {
    text: "Pending",
    style: "bg-red-800 text-red-500",
  },
  [OrderStatus.CANCELLED]: {
    text: "Cancelled",
    style: "bg-gray-800 text-gray-500",
  },
  [OrderStatus.DROPPED]: {
    text: "Dropped",
    style: "bg-yellow-800 text-yellow-500",
  },
  [OrderStatus.CREATED]: {
    text: "Created",
    style: "bg-gray-800 text-gray-500",
  },
};

export const OrderItem = ({
  order,
  dateType,
}: {
  order: TOrder;
  dateType: string;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const router = useRouter();
  const { currentNetwork } = useNetwork();

  const getOrderType = () => {
    if (order?.orderType?.toLocaleLowerCase()?.includes("selldca")) {
      return "Sell DCA";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("sell")) {
      return "Sell";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("buydca")) {
      return "Buy DCA";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("buy")) {
      return "Buy";
    }
    if (
      order?.orderType?.toLocaleLowerCase()?.includes("snipefunzonenewlisting")
    ) {
      return "Snipe Funzone";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("snipedexnewlisting")) {
      return "Snipe Dex";
    }
    return "";
  };

  const getClassType = () => {
    if (order?.orderType?.toLocaleLowerCase()?.includes("sell")) {
      return "text-red-500";
    }
    if (
      order?.orderType?.toLocaleLowerCase()?.includes("buy") ||
      order?.orderType?.toLocaleLowerCase()?.includes("snipe")
    ) {
      return "text-green-500";
    }
    return "";
  };

  const renderOrderAmount = () => {
    return (
      <div className="flex items-center gap-1">
        {(order.orderType.toLocaleLowerCase().includes("buy") ||
          order.orderType.toLocaleLowerCase().includes("snipe")) &&
          (order.tokenQuote.symbol === "SUI" ? (
            <AppLogoNetwork network={NETWORKS.SUI} isBase />
          ) : (
            <AppAvatarToken
              image={
                order?.tokenQuote?.logoImageUrl ||
                order?.tokenQuote?.iconUrl ||
                ""
              }
              size={14}
            />
          ))}
        <AppNumber value={new BigNumber(order.amountIn).abs()} decimals={8} />
      </div>
    );
  };

  const renderOrderAmountOut = () => {
    if (
      !order.amountOut &&
      order.orderType?.toLocaleLowerCase()?.includes("sell") &&
      order?.sellPercent
    ) {
      return (
        <div className="flex items-center gap-1">
          <AppNumber value={order?.sellPercent} decimals={8} />%
        </div>
      );
    }
    if (!order.amountOut) {
      return "--";
    }
    return (
      <div className="flex items-center gap-1">
        {order.orderType.toLocaleLowerCase().includes("sell") && (
          <AppLogoNetwork network={order.network} className="h-4 w-4" isBase />
        )}
        <AppNumber value={new BigNumber(order.amountOut).abs()} decimals={8} />
      </div>
    );
  };

  const openPairDetail = async () => {
    const pair = await rf
      .getRequest("PairRequest")
      .getPair(order.network, order.pairId);
    router.push(`/${pair.network}/${pair.slug}`);
  };

  const _renderTriggerCondition = () => {
    const getTime = () => {
      const duration = moment.duration(order.payload.interval, "seconds");
      const days = Math.floor(duration.asDays());
      const hours = duration.hours();
      const minutes = duration.minutes();

      return `${days >= 1 ? `${days} ${days > 1 ? "days" : "day"}` : ""} ${
        hours >= 1 ? `${hours}h` : ""
      } ${minutes >= 1 ? `${minutes}m` : ""}`;
    };

    if (order?.orderType === "buyDca") {
      return `Buy every ${getTime()}`;
    }

    if (order?.orderType === "sellDca") {
      return `Sell every ${getTime()}`;
    }

    if (order?.orderType?.toLocaleLowerCase()?.includes("sell")) {
      if (order?.payload?.targetPriceQuote) {
        return (
          <div className="flex flex-wrap items-center gap-1">
            Sell{" "}
            {order.payload.sellLimitType === "takeProfit" ? "above" : "below"}
            <AppLogoNetwork
              network={currentNetwork}
              className={`h-4 w-4`}
              isBase
            />
            <AppNumber value={order?.payload?.targetPriceQuote} decimals={8} />
          </div>
        );
      }

      if (!order?.payload?.targetPrice) {
        return "--";
      }

      return (
        <div className="flex flex-wrap gap-1">
          Sell above
          <AppNumber
            isForUSD
            value={order?.payload?.targetPrice}
            decimals={8}
          />
        </div>
      );
    }

    if (!order?.payload?.targetPrice) {
      return "--";
    }

    return (
      <div className="flex flex-wrap gap-1">
        Buy below
        <AppNumber isForUSD value={order?.payload?.targetPrice} decimals={8} />
      </div>
    );
  };

  const _renderPrice = () => {
    if (!order?.price) {
      const amountIn = new BigNumber(order.amountIn).abs();
      const amountOut = order?.amountOut;
      if (
        amountIn &&
        amountOut &&
        order?.orderType?.toLocaleLowerCase()?.includes("buy")
      ) {
        return <AppNumber value={+amountIn / +amountOut} decimals={8} />;
      }
      if (
        amountIn &&
        amountOut &&
        order?.orderType?.toLocaleLowerCase()?.includes("sell")
      ) {
        return <AppNumber value={+amountOut / +amountIn} decimals={8} />;
      }
      return "--";
    }
    return (
      <div className="flex flex-wrap gap-1">
        <AppNumber value={order?.price} decimals={8} />
      </div>
    );
  };

  const _renderOrderStatus = useCallback((status: OrderStatus) => {
    return (
      <div
        className={`rounded-4 flex items-center justify-center px-[4px] py-[1px] ${orderStatusToText[status]?.style}`}
      >
        {orderStatusToText[status]?.text}
      </div>
    );
  }, []);

  const _renderContent = () => {
    if (isMobile) {
      return (
        <div className="bg-white-25 border-white-50 flex w-full gap-[8px] rounded-[4px] border px-[8px] py-[10px]">
          <div onClick={openPairDetail} className="cursor-pointer">
            <BaseToken token={order?.token} dex={order.dex} />
          </div>
          <div className="flex-1">
            <div className="border-white-50 flex justify-between border-b border-dashed pb-[8px]">
              <div>
                <div className="flex items-center gap-1">
                  <div
                    className="body-sm-medium-12 cursor-pointer"
                    onClick={openPairDetail}
                  >
                    {order.token.name}
                  </div>
                  <div className="body-xs-regular-10 text-white-500">
                    {order.token.symbol}/{getSymbolTokenNative(order.network)}
                  </div>
                  <div className=" body-xs-regular-10 text-white-800">
                    {_renderOrderStatus(order.status as OrderStatus)}
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className={`body-xs-regular-10 ${getClassType()}`}>
                    {getOrderType()}
                  </div>
                  <div className="body-xs-regular-10 text-white-500">
                    <AppTimeDisplay
                      timestamp={order.createdAt * 1000}
                      isAgo={false}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-[8px] flex flex-col gap-2">
              <div className="item flex flex-wrap gap-2">
                {/* Amount In */}
                <div className="border-white-50 flex min-w-[110px] items-center gap-1 border-r pr-2">
                  <div className="body-xs-regular-10 text-white-500">
                    Amount In
                  </div>
                  <div className="body-xs-regular-10 text-white-800">
                    {renderOrderAmount()}
                  </div>
                </div>
                {/* Price */}
                <div className="border-white-50 flex min-w-[90px] items-center gap-1 border-r pr-2">
                  <div className="body-xs-regular-10 text-white-500">Price</div>
                  <div className="body-xs-regular-10 text-white-800">
                    {_renderPrice()}
                  </div>
                </div>
                {/* Amount Out */}
                <div className="border-white-50 flex min-w-[110px] items-center gap-1 border-r pr-2">
                  <div className="body-xs-regular-10 text-white-500">
                    Amount Out
                  </div>
                  <div className="body-xs-regular-10 text-white-800">
                    {renderOrderAmountOut()}
                  </div>
                </div>
                {/* Wallet */}
                <div className="flex min-w-[110px] items-center gap-1">
                  <div className="body-xs-regular-10 text-white-500">
                    Wallet
                  </div>
                  <div className="body-xs-regular-10 text-white-800 break-all">
                    {formatShortAddress(order.walletAddress, 5, 3)}
                  </div>
                </div>
                {/* Trigger */}
                <div className="mt-2 flex min-w-[110px] items-center gap-1 sm:mt-0">
                  <div className="body-xs-regular-10 text-white-500">
                    Trigger
                  </div>
                  <div className="body-xs-regular-10 text-white-800 break-all">
                    {_renderTriggerCondition()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex w-full">
        <div className="td text-neutral-alpha-500 w-[8%]">
          <AppTimeDisplay
            timestamp={order.createdAt * 1000}
            isAgo={dateType === DATE_TYPE.AGE}
          />
        </div>

        <div
          className="td flex w-[18%] cursor-pointer items-center gap-[4px]"
          onClick={openPairDetail}
        >
          <BaseToken token={order?.token} dex={order.dex} />
          <div>
            <div className="body-sm-medium-12">{order.token.name}</div>
            <div className="body-2xs-regular-8 text-white-500">
              {order.token.symbol}/{getSymbolTokenNative(order.network)}
            </div>
          </div>
        </div>

        <div className="td w-[10%]">
          <TypeTx type={order.orderType} />
        </div>

        <div className="td w-[10%]">{renderOrderAmount()}</div>

        <div className="td w-[10%]">{_renderPrice()}</div>

        <div className="td w-[10%]">{renderOrderAmountOut()}</div>

        <div className="td flex w-[10%] gap-1">
          {formatShortAddress(order.walletAddress, 5, 3)}
          <CopyIcon
            className="text-neutral-alpha-800 hover:text-neutral-0 h-4 w-4 cursor-pointer"
            onClick={() => copyToClipboard(order.walletAddress)}
          />
          <a
            href={getLinkAddressExplorer(currentNetwork, order.walletAddress)}
            target="_blank"
            className="text-neutral-alpha-800 hover:text-neutral-0"
          >
            <ExternalLink />
          </a>
        </div>

        <div className="td flex w-[15%] justify-end">
          {_renderTriggerCondition()}
        </div>

        <div className="td flex w-[8%] justify-end gap-1">
          {_renderOrderStatus(order.status as OrderStatus)}
        </div>
      </div>
    );
  };

  return <>{_renderContent()}</>;
};

const LIMIT_PER_PAGE = 100;

export const PairOrderHistories = ({
  heightContent,
  pair,
  isHideOtherPair,
}: {
  heightContent: number | string;
  pair?: TPair;
  isHideOtherPair?: boolean;
}) => {
  const { windowWidth } = useWindowSize();
  const { isInitialing } = useInitialing();

  const [dateType, setDateType] = useState<string>("Age");

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const initialHideOtherTokens = Storage.getIsHideOtherTokenForOpenOrder();
  const [isHideOtherTokens, setIsHideOtherTokens] = useState(
    initialHideOtherTokens
  );
  const isHideOtherTokensRef = useRef<boolean>(isHideOtherTokens);

  const dataTableRef = useRef<HTMLDivElement>(null);
  const pairRef = useRef<TPair>({} as TPair);

  const [params, setParams] = useState<any>({
    tradingType: "ALL",
  });
  const paramsRef = useRef<any>({});
  const { currentNetwork } = useNetwork();

  useEffect(() => {
    if (!accessToken || isHideOtherPair === undefined) return;
    isHideOtherTokensRef.current = !!isHideOtherPair;
    setIsHideOtherTokens(isHideOtherTokensRef.current);
  }, [accessToken, isHideOtherPair]);

  useEffect(() => {
    if (!accessToken || !pair) return;
    pairRef.current = pair;
  }, [accessToken, pair, pair?.pairId]);

  useEffect(() => {
    if (isInitialing) return;
    paramsRef.current = params;
    (dataTableRef?.current as any)?.refresh();
  }, [params, isInitialing]);

  useEffect(() => {
    if (pair?.pairId) {
      setParams({
        ...paramsRef.current,
        pairId: isHideOtherTokens ? pair?.pairId : "",
      });
    }
  }, [pair?.pairId, isHideOtherTokens]);

  const getOrderHistories = async (dataTableParams: any) => {
    const paramsFilter = filterParams({
      limit: LIMIT_PER_PAGE,
      ...paramsRef.current,
      ...dataTableParams,
    });
    const res = await rf
      .getRequest("NewOrderRequest")
      .getOrderHistories(currentNetwork, paramsFilter);

    const newData = res?.docs || [];
    return { data: newData };
  };

  const hideOtherTokens = () => {
    isHideOtherTokensRef.current = !isHideOtherTokensRef.current;
    Storage.setIsHideOtherTokenForOpenOrder(isHideOtherTokensRef.current);
    setIsHideOtherTokens(isHideOtherTokensRef.current);
  };

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  if (!accessToken) {
    return <></>;
  }

  return (
    <>
      <AppDataTableRealtime
        isHideHeader={isMobile}
        minWidth={windowWidth <= BREAKPOINT.DESKTOP ? 600 : 790}
        shouldAutoFetchOnInit={false}
        height={heightContent}
        ref={dataTableRef}
        getData={getOrderHistories}
        overrideBodyClassName={isMobile ? "flex flex-col gap-2 mb-2" : ""}
        renderHeader={() => (
          <>
            <div className="thead w-[8%]">
              <AppDateTypeSwitch
                selectedValue={dateType}
                onSelect={setDateType}
              />
            </div>
            <div className="thead w-[18%]">Token</div>
            <div className="thead w-[10%]">Side</div>
            <div className="thead w-[10%]">Amount In</div>
            <div className="thead w-[10%]">Price</div>
            <div className="thead w-[10%]">Amount Out</div>
            <div className="thead w-[10%]">Wallet</div>
            <div className="thead flex w-[15%] justify-end text-right">
              Trigger
            </div>

            {pair ? (
              <div className="thead text-brand-500 action-xs-medium-12 w-[8%] justify-end text-right">
                <div
                  onClick={hideOtherTokens}
                  className={"cursor-pointer whitespace-nowrap"}
                >
                  {isHideOtherTokens ? "Show Others" : "Hide Others"}
                </div>
              </div>
            ) : (
              <div className="thead action-xs-medium-12 w-[8%] justify-end text-right">
                Status
              </div>
            )}
          </>
        )}
        renderRow={(item: TOrder, index: number) => {
          return <OrderItem key={index} order={item} dateType={dateType} />;
        }}
      />
    </>
  );
};
