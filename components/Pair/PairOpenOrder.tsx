import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import {
  ExternalLink,
  TrashIcon,
  TrashSmallIcon,
  CopyIcon,
} from "@/assets/icons";
import { AppDataTableRealtime } from "@/components/AppDataTableRealtime";
// import { FilterByType } from 'src/pages/pool/components/FilterTransaction';
import { useSelector } from "react-redux";
import {
  AppLogoNetwork,
  AppNumber,
  AppTimeDisplay,
  BaseToken,
} from "@/components";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TOrder, TPair, TPairPrice } from "@/types";
import {
  copyToClipboard,
  getLinkAddressExplorer,
  getSymbolTokenNative,
} from "@/utils/helper";
// import { ModalConfirm } from 'src/modals';
import { useMediaQuery } from "react-responsive";
import AppDateTypeSwitch from "@/components/AppDateTypeSwitch";
import { TypeTx } from "@/components/Table/TableMyTrade";
import { BREAKPOINT } from "@/enums/responsive.enum";
import useWindowSize from "@/hooks/useWindowSize";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { DATE_TYPE, NETWORKS } from "@/utils/contants";
import { formatShortAddress } from "@/utils/format";
import Storage from "@/libs/storage";
import moment from "moment";
import BigNumber from "bignumber.js";
import { useRouter } from "next/navigation";
import { usePairPrice } from "@/hooks/usePairPrice";
import { AppAvatarToken } from "../AppAvatarToken";
const OrderItem = ({
  order,
  dateType,
  fetchData,
  pairPrice,
}: {
  order: TOrder;
  dateType: string;
  fetchData: () => void;
  pairPrice: TPairPrice;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const router = useRouter();

  const cancelOrder = async () => {
    try {
      await rf
        .getRequest("NewOrderRequest")
        .cancelOrder(NETWORKS.SUI, order.id);
      toastSuccess("Success", "Cancel successfully!");
      fetchData();
      AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  const getOrderType = () => {
    if (order?.orderType?.toLocaleLowerCase()?.includes("selldca")) {
      return "Sell DCA";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("sell")) {
      return "Sell";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("buydca")) {
      return "Buy DCA";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("buy")) {
      return "Buy";
    }
    return "";
  };

  const getClassType = () => {
    if (order?.orderType?.toLocaleLowerCase()?.includes("sell")) {
      return "text-red-500";
    }
    if (order?.orderType?.toLocaleLowerCase()?.includes("buy")) {
      return "text-green-500";
    }
    return "";
  };

  const renderOrderAmount = () => {
    if (order.orderType.includes("sell")) {
      return `${order.sellPercent}%`;
    }
    return (
      <div className="flex items-center gap-1">
        {order.tokenQuote.symbol === "SUI" ? (
          <AppLogoNetwork network={NETWORKS.SUI} isBase />
        ) : (
          <AppAvatarToken
            image={
              order?.tokenQuote?.logoImageUrl ||
              order?.tokenQuote?.iconUrl ||
              ""
            }
            size={14}
          />
        )}
        <AppNumber value={order.amountIn} decimals={8} />
      </div>
    );
  };

  const openPairDetail = async () => {
    const pair = await rf
      .getRequest("PairRequest")
      .getPair(order.network, order.pairId);
    router.push(`/sui/${pair.slug}`);
  };

  const _renderTriggerCondition = () => {
    const getTime = () => {
      const duration = moment.duration(order.payload.interval, "seconds");
      const days = Math.floor(duration.asDays());
      const hours = duration.hours();
      const minutes = duration.minutes();

      return `${days >= 1 ? `${days} ${days > 1 ? "days" : "day"}` : ""} ${
        hours >= 1 ? `${hours}h` : ""
      } ${minutes >= 1 ? `${minutes}m` : ""}`;
    };

    if (order?.orderType === "buyDca") {
      return `Buy every ${getTime()}`;
    }

    if (order?.orderType === "sellDca") {
      return `Sell every ${getTime()}`;
    }

    if (order?.orderType?.toLocaleLowerCase()?.includes("sell")) {
      if (order?.payload?.targetPriceQuote) {
        return (
          <div className="flex flex-wrap items-center gap-1">
            Sell{" "}
            {order.payload.sellLimitType === "takeProfit" ? "above" : "below"}
            <AppLogoNetwork
              network={NETWORKS.SUI}
              className={`h-4 w-4`}
              isBase
            />
            <AppNumber value={order?.payload?.targetPriceQuote} decimals={8} />
          </div>
        );
      }

      return (
        <div className="flex flex-wrap gap-1">
          Sell{" "}
          {new BigNumber(order.payload.targetPrice).lt(pairPrice?.priceUsd)
            ? "below"
            : "above"}
          <AppNumber
            isForUSD
            value={order?.payload?.targetPrice}
            decimals={8}
          />
        </div>
      );
    }

    return (
      <div className="flex flex-wrap gap-1">
        Buy{" "}
        {new BigNumber(order.payload.targetPrice).lt(pairPrice?.priceUsd)
          ? "below"
          : "above"}
        <AppNumber isForUSD value={order?.payload?.targetPrice} decimals={8} />
      </div>
    );
  };

  const _renderContent = () => {
    if (isMobile) {
      return (
        <div className="bg-white-25 border-white-50 flex w-full gap-[8px] rounded-[4px] border px-[8px] py-[10px]">
          <div onClick={openPairDetail} className="cursor-pointer">
            <BaseToken token={order?.token} dex={order.dex} />
          </div>
          <div className="flex-1">
            <div className="border-white-50 flex justify-between border-b border-dashed pb-[8px]">
              <div>
                <div className="flex items-center gap-1">
                  <div
                    className="body-sm-medium-12 cursor-pointer"
                    onClick={openPairDetail}
                  >
                    {order.token.name}
                  </div>
                  <div className="body-xs-regular-10 text-white-500">
                    {order.token.symbol}/{getSymbolTokenNative(order.network)}
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className={`body-xs-regular-10 ${getClassType()}`}>
                    {getOrderType()}
                  </div>
                  <div className="body-xs-regular-10 text-white-500">
                    <AppTimeDisplay
                      timestamp={order.createdAt * 1000}
                      isAgo={false}
                    />
                  </div>
                </div>
              </div>
              <div
                onClick={cancelOrder}
                className="body-xs-medium-10 cursor-pointer text-red-500"
              >
                Cancel
              </div>
            </div>

            <div className="mt-[8px] flex flex-col gap-2">
              <div className="item flex gap-2">
                <div className="border-white-50 flex items-center gap-1 border-r pr-2">
                  <div className="body-xs-regular-10 text-white-500">
                    Trigger Condition
                  </div>
                  <div className="body-xs-regular-10 text-white-800">
                    {_renderTriggerCondition()}
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  <div className="body-xs-regular-10 text-white-500">Txns</div>
                  <div className="body-xs-regular-10 text-white-800">
                    {order.orderType?.toLowerCase().includes("dca")
                      ? `${order?.numChildOrders} / ${order.payload.repeat}`
                      : "0/1"}
                  </div>
                </div>
              </div>

              <div className="item flex gap-2">
                <div className="border-white-50 flex items-center gap-1 border-r pr-2">
                  <div className=" body-xs-regular-10 text-white-500">
                    Amount
                  </div>
                  <div className=" body-xs-regular-10 text-white-800">
                    {renderOrderAmount()}
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  <div className=" body-xs-regular-10 text-white-500">
                    Wallet
                  </div>
                  <div className=" body-xs-regular-10 text-white-800">
                    {formatShortAddress(order.walletAddress, 5, 3)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex w-full">
        <div className="td text-neutral-alpha-500 w-[10%]">
          <AppTimeDisplay
            timestamp={order.createdAt * 1000}
            isAgo={dateType === DATE_TYPE.AGE}
          />
        </div>

        <div
          className="td flex w-[20%] cursor-pointer items-center gap-[4px]"
          onClick={openPairDetail}
        >
          <BaseToken token={order?.token} dex={order.dex} />
          <div>
            <div className="body-sm-medium-12">{order.token.name}</div>
            <div className="body-2xs-regular-8 text-white-500">
              {order.token.symbol}/{getSymbolTokenNative(order.network)}
            </div>
          </div>
        </div>

        <div className="td w-[10%]">
          <TypeTx type={order.orderType} />
        </div>

        <div className="td w-[10%]">
          {order.orderType?.toLowerCase().includes("dca")
            ? `${order?.numChildOrders} / ${order.payload.repeat}`
            : "0/1"}
        </div>

        <div className="td w-[15%]">{_renderTriggerCondition()}</div>

        <div className="td w-[10%]">{renderOrderAmount()}</div>
        <div className="td flex w-[10%] gap-1">
          {formatShortAddress(order.walletAddress, 5, 3)}
          <CopyIcon
            className="text-neutral-alpha-800 hover:text-neutral-0 h-4 w-4 cursor-pointer"
            onClick={() => copyToClipboard(order.walletAddress)}
          />
          <a
            href={getLinkAddressExplorer(NETWORKS.SUI, order.walletAddress)}
            target="_blank"
            className="text-neutral-alpha-800 hover:text-neutral-0"
          >
            <ExternalLink />
          </a>
        </div>

        <div className="td flex w-[15%] justify-center">
          <TrashIcon className="cursor-pointer" onClick={cancelOrder} />
        </div>
      </div>
    );
  };

  return <>{_renderContent()}</>;
};

export const PairOpenOrders = ({
  heightContent,
  pair,
}: {
  heightContent: number | string;
  pair: TPair;
}) => {
  const { windowWidth } = useWindowSize();
  const { pairPrice } = usePairPrice(pair);

  const [dateType, setDateType] = useState<string>("Age");

  const openOrderRef = useRef<TOrder[]>([]);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const openOrders = useSelector((state: RootState) => state.user.openOrders);
  const initialHideOtherTokens = Storage.getIsHideOtherTokenForOpenOrder();
  const [isHideOtherTokens, setIsHideOtherTokens] = useState(
    initialHideOtherTokens
  );
  const isHideOtherTokensRef = useRef<boolean>(isHideOtherTokens);

  // const networkRef = useRef<string>(network);
  const dataTableRef = useRef<HTMLDivElement>(null);
  const pairRef = useRef<TPair>({} as TPair);

  const handleRefreshOrders = () => {
    (dataTableRef?.current as any)?.refresh();
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.FETCH_ORDERS, handleRefreshOrders);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.FETCH_ORDERS, handleRefreshOrders);
    };
  }, []);

  useEffect(() => {
    openOrderRef.current = openOrders;
    (dataTableRef?.current as any)?.refresh();
  }, [openOrders]);

  useEffect(() => {
    if (!accessToken) return;
    pairRef.current = pair;
  }, [accessToken, pair?.pairId]);

  const hideOtherTokens = () => {
    isHideOtherTokensRef.current = !isHideOtherTokensRef.current;
    Storage.setIsHideOtherTokenForOpenOrder(isHideOtherTokensRef.current);
    setIsHideOtherTokens(isHideOtherTokensRef.current);
  };

  useEffect(() => {
    (dataTableRef.current as any)?.refresh();
  }, [isHideOtherTokens]);

  const getOpenOrders = async (dataTableParams: any) => {
    let filteredOrders = openOrderRef.current || [];

    if (isHideOtherTokensRef.current && pairRef.current?.pairId) {
      filteredOrders = filteredOrders.filter(
        (order: TOrder) => order.pairId === pairRef.current?.pairId
      );
    }

    return { data: filteredOrders };
  };

  // const cancelAllOrders = async () => {
  //   try {
  //     await rf.getRequest('NewOrderRequest').cancelAllOrder(networkRef.current);
  //     toastSuccess('Success', 'Cancel successfully!');
  //     (dataTableRef?.current as any)?.refresh();
  //     AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
  //   } catch (e) {
  //     console.error(e);
  //     toastError('Error', e.massage || 'Something went wrong!');
  //   }
  // };

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  if (!accessToken) {
    return <></>;
  }

  return (
    <>
      <AppDataTableRealtime
        isHideHeader={isMobile}
        minWidth={windowWidth <= BREAKPOINT.DESKTOP ? 600 : 790}
        shouldAutoFetchOnInit={false}
        height={heightContent}
        ref={dataTableRef}
        getData={getOpenOrders}
        overrideBodyClassName={isMobile ? "flex flex-col gap-2 mb-2" : ""}
        renderHeader={() => (
          <>
            <div className="thead w-[10%]">
              <AppDateTypeSwitch
                selectedValue={dateType}
                onSelect={setDateType}
              />
            </div>
            <div className="thead w-[20%]">Token</div>
            <div className="thead w-[10%]">
              Side
              {/*<FilterByType params={params} setParams={setParams} />*/}
            </div>
            <div className="thead w-[10%]">Txns</div>
            <div className="thead w-[15%]">Trigger Condition</div>
            <div className="thead w-[10%]">Amount</div>
            <div className="thead w-[10%]">Wallet</div>

            <div className="thead text-brand-500 action-xs-medium-12 w-[15%] justify-center">
              <div
                onClick={hideOtherTokens}
                className={"cursor-pointer whitespace-nowrap"}
              >
                {isHideOtherTokens ? "Show Other Tokens" : "Hide Other Tokens"}
              </div>
            </div>
          </>
        )}
        renderRow={(item: TOrder, index: number) => {
          return (
            <OrderItem
              pairPrice={pairPrice}
              key={index}
              order={item}
              dateType={dateType}
              fetchData={() => (dataTableRef?.current as any)?.refresh()}
            />
          );
        }}
      />

      {/*{isOpenModalCancelAll && (*/}
      {/*  <ModalConfirm*/}
      {/*    titleAction="Confirm"*/}
      {/*    isOpen={isOpenModalCancelAll}*/}
      {/*    onClose={() => setIsOpenModalCancelAll(false)}*/}
      {/*    onConfirm={cancelAllOrders}*/}
      {/*    description={'Are you sure you want to cancel all orders?'}*/}
      {/*  />*/}
      {/*)}*/}
    </>
  );
};
