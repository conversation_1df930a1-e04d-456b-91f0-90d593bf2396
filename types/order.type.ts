type TToken = {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  logoImageUrl?: string;
  iconUrl?: string;
};

export type TWsOrder = {
  id: string;
  amountIn: string;
  amountOut: string;
  walletAddress: string;
  pairId: string;
  orderType: string;
  token: TToken;
  sellPercent: number;
  version: string;
  network: string;
  hash: string;
  payload: {
    orderSetting: {
      slippage: number;
    };
  };
  status: string;
  reason?: string;
  tradingVolume?: string;
  tradingVolumeUsd?: string;
};

export type TSettingOrder = {
  // slippage: number;
  // tipAmount: string;
  presetId: number;
  quickBuyAmount: string;
  quickSellAmount: string;
  sellSlippage: number;
  sellTipAmount: string;
  buySlippage: number;
  buyTipAmount: string;
  limitBuyGasPrice: number;
  limitBuySlippage: number;
  limitBuyTipAmount: string;
  limitSellGasPrice: number;
  limitSellSlippage: number;
  limitSellTipAmount: string;
  quickBuyGasPrice: number;
  quickBuySlippage: number;
  quickBuyTipAmount: string;
  quickSellGasPrice: number;
  quickSellSlippage: number;
  quickSellTipAmount: string;
  sellGasPrice: number;
  snipeGasPrice: number;
  snipeSlippage: number;
  snipeTipAmount: string;
  copyTradeTipAmount: string;
  copyTradeGasPrice: number;
  copyTradeSlippage: number;
};

export type TOrder = {
  amountIn: number;
  amountOut: number;
  dex: {
    dex: string;
    name: string;
    network: "aptos";
    version: "string";
  };
  hash: string;
  id: string;
  network: string;
  orderType: string;
  pairId: string;
  payload: {
    orderSetting: {
      priorityFee: string;
      slippage: number;
    };
    sellLimitType: string;
    startPrice: string;
    targetPrice: string;
    triggerPrice: string;
    targetPriceQuote: string;
    interval: number;
    repeat: number;
  };
  metadata?: {
    startMc?: string;
    startPrice?: string;
    targetMcPercent?: string;
    targetPricePercent?: string;
    price?: string;
  };
  poolId: string;
  reason: string;
  sellPercent: number;
  status: string;
  createdAt: number;
  token: {
    address: string;
    bannerImageUrl: string;
    createdAt: string;
    decimals: number;
    logoImageUrl: string;
    name: string;
    network: string;
    price: string;
    priceUsd: string;
    symbol: string;
    totalSupply: string;
    updatedAt: string;
    volumeUsd: string;
  };
  tradingFee: number;
  tradingFeeUsd: number;
  version: number;
  walletAddress: string;
  timestamp: number;
  tradingVolumeUsd: string;
  tradingVolume: string;
  priceUsd: string;
  price: string;
  numChildOrders: number;
  tokenQuote: TToken;
  convertRoutes?: {
    amountIn: string;
    amountOut: string;
  }[];
};
