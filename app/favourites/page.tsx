"use client";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { BoxQuickBuy, MyPositions, PairListType } from "@/components/ListPair";
import React, { useState, useMemo } from "react";
import { useMediaQuery } from "react-responsive";
import { TableFavouritePair } from "@/components/Table/TableFavouritePair";
import Link from "next/link";
import { ROUTE_PATH } from "../../routes/index";
import { StarIcon24 } from "@/assets/icons";

export default function FavouritePage() {
  const [buyAmount, setBuyAmount] = useState<any>("1");

  const hasPosition = useSelector(
    (state: RootState) => state.user.positions?.length > 0
  );

  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const tableHeight = useMemo(() => {
    const baseHeight = isMobile ? 158 : 122;
    const offset = hasPosition ? (isMobile ? 52 : 40) : 0;

    return `calc(100vh - ${baseHeight + offset}px)`;
  }, [hasPosition, isMobile]);

  if (isMobile) {
    return (
      <>
        <div className="mx-[8px]">
          <PairListType className="mt-[12px]" />
          <div className="my-[8px] flex items-center justify-between gap-[8px] md:w-auto">
            <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
          </div>
          <TableFavouritePair tableHeight={tableHeight} buyAmount={buyAmount} />
        </div>
      </>
    );
  }

  return (
    <>
      <MyPositions />
      <div className="mx-2 mb-[16px] flex items-center justify-between gap-4 pt-[12px] md:mx-[20px]">
        <div className="border-white-50 flex w-full items-center gap-[20px] border-b pb-2 md:w-1/2 lg:pb-0">
          <Link href={ROUTE_PATH.FAVOURITES}>
            <div className="text-brand-500 flex flex-col items-center ">
              <StarIcon24 />
              <div className="bg-brand-500 mt-1 h-[2px] w-[24px] rounded-[100px]"></div>
            </div>
          </Link>
          <Link href={"/new-pairs"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer pb-2 text-[14px] font-medium leading-[1.2] md:text-[18px]">
              New Pairs
            </div>
          </Link>
          <Link href={"/funzone"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer pb-2 text-[14px] font-medium leading-[1.2] md:text-[18px]">
              Fun Zone
            </div>
          </Link>
          <Link href={"/trending"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer pb-2 text-[14px] font-medium leading-[1.2] md:text-[18px]">
              Trending
            </div>
          </Link>
        </div>
        <div className="flex items-center justify-between gap-[8px] md:w-auto">
          <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
        </div>
      </div>

      <TableFavouritePair tableHeight={tableHeight} buyAmount={buyAmount} />
    </>
  );
}
