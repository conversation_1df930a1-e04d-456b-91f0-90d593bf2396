"use client";
import * as React from "react";
import { memo, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import {
  BoxQuickBuy,
  ButtonFilterPair,
  MyPositions,
  PairListType,
} from "@/components/ListPair";
import { TableNewPair } from "@/components/Table/TableNewPair";
import useWindowSize from "@/hooks/useWindowSize";
import Storage from "@/libs/storage";
import { RootState } from "@/store";
import Link from "next/link";
import { isIOSMobile } from "@/utils/helper";
import { PAIR_FILTER_STORAGE_KEY } from "@/constants";
import { StarIcon24 } from "@/assets/icons";
import { ROUTE_PATH } from "../../routes/index";

export default function NewPairsPage() {
  const [buyAmount, setBuyAmount] = useState<any>("1"); // set default buy amount
  const [params, setParams] = useState<any>(
    Storage.getPairSearch(PAIR_FILTER_STORAGE_KEY)
  );

  const hasPosition = useSelector(
    (state: RootState) => state.user.positions?.length > 0
  );

  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const { windowHeight } = useWindowSize();
  const tableHeight = useMemo(() => {
    if (isMobile) {
      //ios
      if (isIOSMobile()) {
        if (!isHideInstallApp) {
          return `calc(${windowHeight}px - 55px - 46px - 48px - 59px - 40px)`;
        }
        return `calc(${windowHeight}px - 55px - 46px - 48px - 59px)`;
      }

      // android
      if (!isHideInstallApp) {
        return `calc(${windowHeight}px - 55px - 46px - 48px - 48px - 40px)`;
      }
      return `calc(${windowHeight}px - 55px - 46px - 48px - 48px)`;
    }
    return hasPosition ? `${windowHeight - 162}px` : `${windowHeight - 130}px`;
  }, [isMobile, windowHeight, hasPosition]);

  if (isMobile) {
    return (
      <>
        <div className="mx-[8px]">
          <PairListType className="mt-[12px]" />
          <div className="my-[8px] flex items-center justify-between gap-[8px] md:w-auto">
            <ButtonFilterPair
              params={params}
              setParams={setParams}
              height={32}
              showText={false}
            />
            <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
          </div>
          <TableNewPair
            params={params}
            buyAmount={buyAmount}
            tableHeight={tableHeight}
          />
        </div>
      </>
    );
  }

  return (
    <>
      <MyPositions />
      <div className="mx-2 mb-[16px] flex justify-between gap-4 pt-[12px] md:mx-[20px]">
        <div className="border-white-50 flex w-full items-center gap-[20px] border-b pb-2 md:w-1/2 lg:pb-0">
          <Link href={ROUTE_PATH.FAVOURITES}>
            <div className="text-white-800 flex flex-col items-center ">
              <StarIcon24 />
            </div>
          </Link>
          <div className="text-brand-500 mb-[-6px] flex flex-col items-center text-[18px] font-semibold leading-[1.2] md:mb-[-8px] md:text-[24px]">
            New Pairs
            <div className="bg-brand-500 h-[2px] w-[24px] rounded-[100px]"></div>
          </div>
          <Link href={"/funzone"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer text-[14px] font-medium leading-[1.2] md:text-[18px]">
              Fun Zone
            </div>
          </Link>
          <Link href={"/trending"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer text-[14px] font-medium leading-[1.2] md:text-[18px]">
              Trending
            </div>
          </Link>
        </div>
        <div className="flex items-center justify-between gap-[8px] md:w-auto">
          <ButtonFilterPair params={params} setParams={setParams} />
          <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
        </div>
      </div>
      <TableNewPair
        params={params}
        buyAmount={buyAmount}
        tableHeight={tableHeight}
      />
    </>
  );
}
